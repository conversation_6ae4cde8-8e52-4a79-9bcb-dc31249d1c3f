jQuery(function ($) {
	$(document).ready(function () {
		
		setTimeout(function(){
			jQuery('.elementor-field-type-date input[type="text"]').each(function () {
				jQuery(this).removeAttr('pattern');
				
				flatpickr(this, {
					dateFormat: "d.m.Y",
				});
				jQuery(this).val(jQuery(this).attr('value'));
	  		});
	  	}, 1000);

		var city_language_trigger = function () {
			let countryDropdown = $('select[name="form_fields[country]"]');
			if (countryDropdown.length) {
				let stateDropdown = $('select[name="form_fields[state]"]');
				let countryStateData = stateDropdown.data('states');
				let languageDropdown = $('select[name="form_fields[preferred_language]"]');
				let preSelectedState = stateDropdown.data('state-selected'); // Get the pre-selected state if available

				function updateDropdowns(selectedCountry) {
					// Clear previous options
					stateDropdown.empty();

					if (selectedCountry === 'Switzerland') {
						languageDropdown.closest('.elementor-field-type-select').show();
						languageDropdown.attr('required', 'required');
					} else {
						languageDropdown.closest('.elementor-field-type-select').hide();
						languageDropdown.removeAttr('required');
					}

					// Populate and show state dropdown if states are available
					if (countryStateData[selectedCountry]) {
						$.each(countryStateData[selectedCountry], function (index, state) {
							var value_option = (state !== '--- Please Select ---') ? state : '';
							let selected = (preSelectedState && preSelectedState === state) ? 'selected' : '';
							stateDropdown.append('<option value="' + value_option + '" ' + selected + '>' + state + '</option>');
						});

						stateDropdown.closest('.elementor-field-type-select').show();
						stateDropdown.attr('required', 'required');
					} else {
						stateDropdown.closest('.elementor-field-type-select').hide();
						stateDropdown.removeAttr('required');
					}
				}

				// Check on page load if a country is preselected
				let initialSelectedCountry = countryDropdown.val();
				if (initialSelectedCountry && initialSelectedCountry !== '') {
					updateDropdowns(initialSelectedCountry);
				}

				// Listen for changes in the country dropdown
				countryDropdown.on('change', function () {
					let selectedCountry = $(this).val();
					preSelectedState = null; // Reset pre-selected state on country change
					updateDropdowns(selectedCountry);
				});
			}
		}

		// Run on Elementor popup show
		$(document).on('elementor/popup/show', function () {
			city_language_trigger();
		});

		// Run on page load
		city_language_trigger();

		// Handle dongle_no field behavior for user update form
		var handle_dongle_no_field = function() {
			let dongleField = $('input[name="form_fields[dongle_no]"]');
			let emailField = $('input[name="form_fields[email]"]');

			if (dongleField.length && emailField.length && emailField.prop('disabled')) {
				// This is user update form - always make dongle field optional
				dongleField.removeAttr('required');
				dongleField.removeAttr('aria-required');
				dongleField.closest('.elementor-field-group').removeClass('elementor-field-required');
				dongleField.removeClass('elementor-field-textual');

				// Remove asterisk from label if present
				let label = dongleField.closest('.elementor-field-group').find('label');
				if (label.length) {
					let labelText = label.html();
					if (labelText && labelText.includes(' *')) {
						label.html(labelText.replace(' *', ''));
					}
				}

				// Mark field as optional for Elementor validation
				dongleField.attr('data-optional', 'true');

				// Check if field has a data-original-value and restore it if field is empty
				let originalValue = dongleField.attr('data-original-value');
				let currentValue = dongleField.val();

				// Only restore if field is completely empty
				if (originalValue && !currentValue) {
					dongleField.val(originalValue);
				}

				// Check for duplicated values and fix them
				if (currentValue && currentValue.includes(' ')) {
					// If value contains spaces, it might be duplicated
					let parts = currentValue.split(' ');
					if (parts.length === 2 && parts[0] === parts[1]) {
						// It's duplicated, use only the first part
						dongleField.val(parts[0]);
					}
				}

				// Debug: Log field value status
				console.log('Dongle field - Current:', currentValue, 'Original:', originalValue, 'Final:', dongleField.val());
			}
		};

		// Override Elementor form validation for dongle_no field
		var override_elementor_validation = function() {
			// Function to make dongle field optional in user update form
			function makeDongleOptional() {
				let dongleField = $('input[name="form_fields[dongle_no]"]');
				let emailField = $('input[name="form_fields[email]"]');

				// Check if this is user update form (email field is disabled)
				if (dongleField.length && emailField.length && emailField.prop('disabled')) {
					// Always make dongle field optional in user update form
					dongleField.removeAttr('required');
					dongleField.removeAttr('aria-required');
					dongleField.closest('.elementor-field-group').removeClass('elementor-field-required');

					// Remove asterisk from label
					let label = dongleField.closest('.elementor-field-group').find('label');
					if (label.length) {
						let labelText = label.html();
						if (labelText && labelText.includes(' *')) {
							label.html(labelText.replace(' *', ''));
						}
					}

					// Remove any existing error messages
					dongleField.closest('.elementor-field-group').find('.elementor-message').remove();
					dongleField.removeClass('elementor-error');
					dongleField.removeAttr('aria-invalid');

					// Check for and fix duplicated values
					let currentValue = dongleField.val();
					if (currentValue && currentValue.includes(' ')) {
						// If value contains spaces, it might be duplicated
						let parts = currentValue.split(' ');
						if (parts.length === 2 && parts[0] === parts[1]) {
							// It's duplicated, use only the first part
							dongleField.val(parts[0]);
						}
					}
				}
			}

			// Run immediately
			makeDongleOptional();

			// Run on form events
			$(document).on('submit', '.elementor-form', function(e) {
				makeDongleOptional();
			});

			// Also handle real-time validation
			$(document).on('blur change focus', 'input[name="form_fields[dongle_no]"]', function() {
				makeDongleOptional();
			});

			// Run periodically to catch any dynamic changes
			setInterval(makeDongleOptional, 1000);
		};

		// Fix any existing duplicated dongle values immediately
		var fix_duplicated_dongle_values = function() {
			let dongleField = $('input[name="form_fields[dongle_no]"]');
			if (dongleField.length) {
				let currentValue = dongleField.val();
				if (currentValue && currentValue.includes(' ')) {
					let parts = currentValue.split(' ');
					if (parts.length === 2 && parts[0] === parts[1]) {
						dongleField.val(parts[0]);
						console.log('Fixed duplicated dongle value:', parts[0]);
					}
				}
			}
		};

		// Run immediate fix
		fix_duplicated_dongle_values();

		// Run dongle field handler on page load
		handle_dongle_no_field();

		// Initialize validation override
		override_elementor_validation();

		// Run on Elementor popup show
		$(document).on('elementor/popup/show', function () {
			fix_duplicated_dongle_values();
			handle_dongle_no_field();
		});


		$('.elementor-nav-menu .menu-item-has-children > a').hover(
			function (e) {
				// Mouse Enters
				e.stopPropagation();
				e.preventDefault();
			}
		);

		$('.software-details').each(function () {
			if ($(this).find('.version-select').length) {
				const downloadButton = $(this).find('.download-button');
				const yearDisplay = $(this).find('h4.year-display');
				const originalText = yearDisplay.data('original-text');
				$(this).find('.version-select').on('change', function () {
					const selectedYear = $(this).find('option:selected').text();
					downloadButton.attr('href', $(this).val());
					yearDisplay.text(originalText.replace('{year}', selectedYear));
				});
			}
		});



		// Get the current page URL path (normalized)
		var currentPath = window.location.pathname.replace(/\/$/, "");

		// Variable to track if the class has been added
		let classAdded = false;

		// Loop through each menu item that has children
		$('.elementor-nav-menu .menu-item-has-children').each(function () {
			var $this = $(this);

			// Loop through each submenu link
			$this.find('ul.sub-menu li a').each(function () {
				// Get the relative URL from the href attribute
				var subMenuHref = new URL($(this).attr('href'), window.location.origin).pathname.replace(/\/$/, ""); // Normalize to remove trailing slashes

				// Check if the current page URL matches the submenu href and contains more than just '/highlights'
				if (!classAdded && currentPath === subMenuHref && currentPath.split('/').length > 2) {
					// Add the elementor-item-active class to the parent menu item
					$this.children('a').addClass('elementor-item-active');

					// Mark the class as added
					classAdded = true;
				}
			});
		});


		$('.elementor-nav-menu .menu-item-has-children > a').on('click', function (e) {
			e.preventDefault();
			var title_sidebar = $(this).text();
			var subMenu = $(this).next('ul').html();

			// Open the popup via Elementor's method
			elementorProFrontend.modules.popup.showPopup({ id: 640 });

			// Wait for the popup to open and then modify the display
			setTimeout(function () {
				$('.sidebar-navigation-popup .sidebar-title').html(title_sidebar);
				$('.mobile-sidebar-menu .sidebar-title').html(title_sidebar);
				$('.sidebar-navigation-popup .menu').html(subMenu);
			}, 100);
		});


		$('.header.tooltip-show').each(function () {
			var tooltipText = $(this).attr('data-tooltip');
			var tooltip = $('<div class="tooltip"></div>').text(tooltipText);

			$(this).addClass('tooltip-container').append(tooltip);

			$(this).hover(
				function () {
					$(this).find('.tooltip').css({ 'visibility': 'visible', 'opacity': '1' });
				},
				function () {
					$(this).find('.tooltip').css({ 'visibility': 'hidden', 'opacity': '0' });
				}
			);
		});

		$('.ue-grid-item-meta-data:nth-child(2)').hide();

		$('.latest-news-wrp.tooltip-show .owl-stage .owl-item > div').each(function () {
			// Get the text from the element with the class '.uc_post_title'
			var titleText = $(this).find('.uc_post_title').text();

			// Create the tooltip element with the title text
			var tooltip = $('<div class="tooltip"></div>').text(titleText);

			// Set initial CSS for the tooltip (hidden and transparent)
			tooltip.css({ 'visibility': 'hidden', 'opacity': '0' });

			// Add the tooltip to the current element
			$(this).addClass('tooltip-container').append(tooltip);

			// Set hover behavior to show and hide the tooltip
			$(this).hover(
				function () {
					$(this).find('.tooltip').css({ 'visibility': 'visible', 'opacity': '1' });
				},
				function () {
					$(this).find('.tooltip').css({ 'visibility': 'hidden', 'opacity': '0' });
				}
			);
		});

		// Tooltip for Customer/Partner
		$('.elementor-widget-ucaddon_customer_partner_post_grid .ue-item').hover(
			function () {
				// On hover, make the tooltip visible
				$(this).find('.tooltip').css({ 'visibility': 'visible', 'opacity': '1' });
			},
			function () {
				// On mouse out, hide the tooltip
				$(this).find('.tooltip').css({ 'visibility': 'hidden', 'opacity': '0' });
			}
		);



		// tooltip for Dashboard
		$('.progress').each(function () {
			var tooltipText = $(this).attr('data-title');
			var tooltip = $('<div class="tooltip"></div>').text(tooltipText);

			$(this).addClass('tooltip-container').append(tooltip);

			$(this).hover(
				function () {
					$(this).find('.tooltip').css({ 'visibility': 'visible', 'opacity': '1' });
				},
				function () {
					$(this).find('.tooltip').css({ 'visibility': 'hidden', 'opacity': '0' });
				}
			);
		});

		$('.status-trophies-box > span').each(function () {
			var tooltipText = $(this).attr('data-title');
			var tooltip = $('<div class="tooltip"></div>').text(tooltipText);

			$(this).addClass('tooltip-container').append(tooltip);

			$(this).hover(
				function () {
					$(this).find('.tooltip').css({ 'visibility': 'visible', 'opacity': '1' });
				},
				function () {
					$(this).find('.tooltip').css({ 'visibility': 'hidden', 'opacity': '0' });
				}
			);
		});


		$('.info-icon').each(function () {
			var tooltipText = $(this).attr('data-title');
			var tooltip = $('<div class="tooltip"></div>').text(tooltipText);

			$(this).addClass('tooltip-container').append(tooltip);

			$(this).hover(
				function () {
					$(this).find('.tooltip').css({ 'visibility': 'visible', 'opacity': '1' });
				},
				function () {
					$(this).find('.tooltip').css({ 'visibility': 'hidden', 'opacity': '0' });
				}
			);
		});

		//Tooltip for Remove License Button

		$('#remove-license-btn').each(function () {
			var tooltipText = $(this).attr('data-title');
			var tooltip = $('<div class="tooltip"></div>').text(tooltipText);

			$(this).addClass('tooltip-container').append(tooltip);

			$(this).hover(
				function () {
					$(this).find('.tooltip').css({ 'visibility': 'visible', 'opacity': '1' });
				},
				function () {
					$(this).find('.tooltip').css({ 'visibility': 'hidden', 'opacity': '0' });
				}
			);
		});


		//Tooltip for Add License Button


		$('#toggle-add-license-btn').each(function () {
			var tooltipText = $(this).attr('data-title');
			var tooltip = $('<div class="tooltip"></div>').text(tooltipText);

			$(this).addClass('tooltip-container').append(tooltip);

			$(this).hover(
				function () {
					$(this).find('.tooltip').css({ 'visibility': 'visible', 'opacity': '1' });
				},
				function () {
					$(this).find('.tooltip').css({ 'visibility': 'hidden', 'opacity': '0' });
				}
			);
		});


		//Tooltip for Add Module Button
		$('.add-to-subscription').each(function () {
			var tooltipText = $(this).attr('data-title');
			var tooltip = $('<div class="tooltip"></div>').text(tooltipText);

			$(this).addClass('tooltip-container').append(tooltip);

			$(this).hover(
				function () {
					$(this).find('.tooltip').css({ 'visibility': 'visible', 'opacity': '1' });
				},
				function () {
					$(this).find('.tooltip').css({ 'visibility': 'hidden', 'opacity': '0' });
				}
			);
		});


		//Select To Select 2
		setTimeout(function () { jQuery('select').select2(); }, 500);


		// Reinitialize on Elementor Popups
		$(document).on('elementor/popup/show', function () {
			setTimeout(function () { jQuery('select').select2(); }, 500);
		});

		// 	Search Toggle Functionality 
		$("#open_search_wrp").click(function () {
			event.preventDefault(); // Prevents the default action, such as page refresh
			$("#search_field_wrp").stop(true, true).slideToggle(200); // Slide with a 400ms duration
		});

		$("#search_field_wrp .elementor-search-form__submit").append("<span class='elementor-button-icon'><i aria-hidden='true' class='far fa-search'></i></span>");


		//Language Switcher
		$(".language-button").click(function () {
			event.preventDefault(); // Prevents the default action, such as page refresh
			$("#language-switcher-wrp").stop(true, true).slideToggle(200); // Slide with a 400ms duration
		});



		// Toggle the visibility of the calendar options on click of the toggle button
		$('.calendar-toggle').click(function (event) {
			event.stopPropagation(); // Prevent click from propagating to document

			// Close any open calendar options except the current one
			$('.calendar-options').not($(this).closest('.calendar-dropdown').find('.calendar-options')).hide();

			// Toggle the visibility of the clicked calendar's options
			$(this).closest('.calendar-dropdown').find('.calendar-options').toggle();
		});

		// Hide the calendar options if clicking anywhere outside the calendar-dropdown
		$(document).click(function () {
			$('.calendar-options').hide();
		});


	});

	jQuery(window).on('load', function () {
		jQuery.each(elementorFrontend.documentsManager.documents, (id, document) => {
			if (document.getModal) { // Check if it's a popup document
				document.getModal().on('show', () => {
					// Handle menu item click
					jQuery('.back-btn').hide(); // Hide back button initially


					//Add Class on body No Scroll
					jQuery('body').addClass('no-scroll'); // Prevent scrolling

					jQuery('.elementor-nav-menu .menu-item-has-children > a').on('click', function (e) {
						e.preventDefault();


						// Close all other submenus
						jQuery('.elementor-nav-menu .menu-item-has-children > ul.visible').animate({ "right": "-1000px", "opacity": "0" }, "slow").removeClass('visible');
						jQuery('.back-btn').hide(); // Hide back button when switching menus

						// Get the current submenu
						var subMenu = jQuery(this).next('ul');

						// Wait for the popup to open and then modify the display
						var title_sidebar = jQuery(this).text();
						setTimeout(function () {
							jQuery('.mobile-sidebar-menu .sidebar-title > div > h2').text(title_sidebar);
							jQuery('.mobile-sidebar-menu .breadcrumbs > div > h2').text('SolidCAM / ' + title_sidebar);

						}, 400);

						// Check if the current submenu is already visible
						if (subMenu.hasClass('visible')) {
							// Close the current submenu and hide the back button
							subMenu.animate({ "right": "-1000px", "opacity": "0" }, "slow").removeClass('visible');
							jQuery('.back-btn').hide(); // Hide back button when closing
						} else {
							// Open the current submenu and show the back button
							subMenu.animate({ "right": "0px", "opacity": "1" }, "slow").addClass('visible');
							setTimeout(function () {
								jQuery('.back-btn').show(); // Show back button when opening
							}, 500);
						}


					});

					// Handle back button click
					jQuery('.back-btn').on('click', function () {
						// Find the visible submenu and close it
						jQuery('.elementor-nav-menu .menu-item-has-children > ul.visible').animate({ "right": "-1000px", "opacity": "0" }, "slow").removeClass('visible');
						jQuery(this).hide(); // Hide the back button when submenu is closed
						jQuery('.mobile-sidebar-menu .sidebar-title > div > h2').text('SolidCAM');
						jQuery('.mobile-sidebar-menu .breadcrumbs > div > h2').text('SolidCAM');

					});
				});

				// Remove no-scroll class when popup is closed
				document.getModal().on('hide', () => {
					jQuery('body').removeClass('no-scroll'); // Allow scrolling again
				});
			}
		});
	});

	// jQuery(window).on('load', function () {
	// 	jQuery.each(elementorFrontend.documentsManager.documents, (id, document) => {
	// 		if (document.getModal) {
	// 			document.getModal().on('show', () => {
	// 				jQuery('.back-btn').hide(); // Hide back button initially
	// 				jQuery('body').addClass('no-scroll'); // Prevent scrolling

	// 				// Delegate the submenu click to a stable parent container
	// 				jQuery(document).off('click', '.elementor-nav-menu .menu-item-has-children > a').on('click', '.elementor-nav-menu .menu-item-has-children > a', function (e) {
	// 					e.preventDefault();

	// 					// Close all other submenus
	// 					jQuery('.elementor-nav-menu .menu-item-has-children > ul.visible')
	// 						.animate({ "right": "-1000px", "opacity": "0" }, "slow")
	// 						.removeClass('visible');
	// 					jQuery('.back-btn').hide();

	// 					var subMenu = jQuery(this).next('ul');
	// 					var title_sidebar = jQuery(this).text();

	// 					setTimeout(function () {
	// 						jQuery('.mobile-sidebar-menu .sidebar-title > div > h2').text(title_sidebar);
	// 						jQuery('.mobile-sidebar-menu .breadcrumbs > div > h2').text('SolidCAM / ' + title_sidebar);
	// 					}, 400);

	// 					if (subMenu.hasClass('visible')) {
	// 						subMenu.animate({ "right": "-1000px", "opacity": "0" }, "slow").removeClass('visible');
	// 						jQuery('.back-btn').hide();
	// 					} else {
	// 						subMenu.animate({ "right": "0px", "opacity": "1" }, "slow").addClass('visible');
	// 						setTimeout(function () {
	// 							jQuery('.back-btn').show();
	// 						}, 500);
	// 					}
	// 				});

	// 				// Delegate back button click
	// 				jQuery(document).off('click', '.back-btn').on('click', '.back-btn', function () {
	// 					jQuery('.elementor-nav-menu .menu-item-has-children > ul.visible')
	// 						.animate({ "right": "-1000px", "opacity": "0" }, "slow")
	// 						.removeClass('visible');
	// 					jQuery(this).hide();
	// 					jQuery('.mobile-sidebar-menu .sidebar-title > div > h2').text('SolidCAM');
	// 					jQuery('.mobile-sidebar-menu .breadcrumbs > div > h2').text('SolidCAM');
	// 				});
	// 			});

	// 			// Remove no-scroll class when popup is closed
	// 			document.getModal().on('hide', () => {
	// 				jQuery('body').removeClass('no-scroll');
	// 			});
	// 		}
	// 	});
	// });

	// Need Discussion

	// Handle login form submission
	$(document).on('keyup', '.um-form-field', function (e) {
		$(this).closest('.um-field').find('.um-field-error').remove();
	});


	$(document).on('submit', '.um-login form', function (e) {
		e.preventDefault();

		var $form = $(this);
		var $wrapper = $form.closest('.um-login');

		// Add loading state
		$wrapper.addClass('um-loading');

		// Prepare form data
		var formData = $form.serialize();
		formData += '&action=um_ajax_login&nonce=' + mss_ajax.login_nonce;

		// Send Ajax request
		$.ajax({
			url: mss_ajax.ajax_url,
			type: 'POST',
			data: formData,
			success: function (response) {
				if (response.success) {
					// Show success message
					if ($wrapper.find('.um-notice').length === 0) {
						$wrapper.prepend('<div class="um-notice success"></div>');
					}
					$wrapper.find('.um-notice')
						.removeClass('err')
						.addClass('success')
						.html(response.data.message)
						.show();

					// Redirect if URL provided
					if (response.data.redirect) {
						window.location.href = response.data.redirect;
					}
				} else {
					// Remove existing error messages
					$wrapper.find('.um-field-error').remove();

					// Loop through the error messages and append them dynamically
					$.each(response.data.message, function (field, errorMessage) {
						const fieldSelector = `.um-field-${field}`; // Dynamic field class
						const errorHtml = `
							<div class="um-field-error" id="um-error-for-${field}">
								${errorMessage}
							</div>
						`;

						// Append the error message to the corresponding field
						$wrapper.find(fieldSelector)
							.removeClass('success')
							.addClass('err')
							.append(errorHtml);
					});
				}
			},
			error: function () {
				// Show generic error message
				if ($wrapper.find('.um-notice').length === 0) {
					$wrapper.prepend('<div class="um-notice err"></div>');
				}
				$wrapper.find('.um-notice')
					.removeClass('success')
					.addClass('err')
					.html('An error occurred. Please try again.')
					.show();
			},
			complete: function () {
				// Remove loading state
				$wrapper.removeClass('um-loading');
				$wrapper.find('#um-submit-btn').prop('disabled', false);
			}
		});
	});



	// 	Star Filter All Button enabled
	$('.ue_star_item').on('click', function () {
		// Check if the clicked label has the 'uc-selected' class
		if ($(this).hasClass('uc-selected')) {
			// Remove 'uc-hidden' class and 'disabled' attribute from the button
			$('.uc-button-clear').removeClass('uc-hidden').removeAttr('disabled');
		}
	});
});