<?php

// Form acceptance field add Terms URL

add_action( 'elementor/element/form/section_form_fields/before_section_end', 'enable_acceptance_field_link_control', 100, 2 );

function enable_acceptance_field_link_control( $element, $args ) {
	$elementor = \Elementor\Plugin::instance();
	$control_data = $elementor->controls_manager->get_control_from_stack( $element->get_name(), 'form_fields' );

	if ( is_wp_error( $control_data ) ) {
		return;
	}
	
	$tmp = new \Elementor\Repeater();
	
	$tmp->add_control(
		'acceptance_link_text',
		[
			'label' => esc_html__( 'Term Link Text', 'elementor' ),
			'type' => \Elementor\Controls_Manager::TEXT,
			'label_block' => true,
			'placeholder' => esc_html__( 'Terms', 'elementor' ),
			'default' => '',
			'dynamic' => [
				'active' => true,
			],
			'conditions' => [
				'terms' => [
					[
						'name' => 'field_type',
						'operator' => 'in',
						'value' => ['acceptance'],
					],
				],
			],
		]
	);
	
	$tmp->add_control(
		'acceptance_link',
		[
			'label' => esc_html__( 'Link', 'elementor' ),
			'type' => \Elementor\Controls_Manager::URL,
			'dynamic' => [
				'active' => true,
			],
			'placeholder' => esc_html__( 'https://your-link.com', 'elementor' ),
			'conditions' => [
				'terms' => [
					[
						'name' => 'field_type',
						'operator' => 'in',
						'value' => ['acceptance'],
					],
				],
			],
		]
	);

	$pattern_field = $tmp->get_controls();
	$acceptance_link_text = $pattern_field['acceptance_link_text'];
	$acceptance_link = $pattern_field['acceptance_link'];

	// insert new class field in advanced tab before field ID control
	$new_order = [];
	foreach ( $control_data['fields'] as $field_key => $field ) {
		if ( 'acceptance_text' === $field['name'] ) {
			$new_order['acceptance_link_text'] = $acceptance_link_text;
			$new_order['acceptance_link'] = $acceptance_link;
		}
		$new_order[ $field_key ] = $field;
	}
	$control_data['fields'] = $new_order;

	$element->update_control( 'form_fields', $control_data );
}

add_filter( 'elementor_pro/forms/render/item', 'custom_render_acceptance_link', 10, 3 );

function custom_render_acceptance_link( $field, $field_index, $form_widget ) {
	$form_id = $form_widget->get_settings('form_id');
	
	// Check if the field type is 'acceptance'
	if ( $form_id == 'user_update_form' && 'email' === $field['field_type'] ) {
		$form_widget->add_render_attribute( 'input' . $field_index, 'disabled', 'disabled' );
	} else if ( $form_id == 'user_update_form' && $field['custom_id'] == 'dongle_no' && $field['field_value'] != '' ) {
		$form_widget->add_render_attribute( 'input' . $field_index, '', 'disabled' );
	} else if ( 'acceptance' === $field['field_type'] ) {
		// Get the custom acceptance link text and URL
		$acceptance_link_text = isset( $field['acceptance_link_text'] ) ? $field['acceptance_link_text'] : '';
		$acceptance_link = isset( $field['acceptance_link']['url'] ) ? $field['acceptance_link']['url'] : '';

		// Append the link to the acceptance text output
		if ( $acceptance_link_text && $acceptance_link ) {
			// Build the HTML for the link
			$link_html = sprintf(
				' <a href="%s" target="_blank" rel="nofollow noopener">%s</a>',
				esc_url( $acceptance_link ),
				esc_html( $acceptance_link_text )
			);

			// Append the link to the field's label
			if ( isset( $field['acceptance_text'] ) ) {
				$field['acceptance_text'] .= $link_html;
			}
		}
	} else if ( $field['custom_id'] == 'dongle_no' ) {
		
		$form_widget->add_render_attribute( 'input' . $field_index, 'pattern', '^(\d{5}|\d{17}|\d{18}|[a-zA-Z0-9]{8}(-[a-zA-Z0-9]{4}-[a-zA-Z0-9]{4}-[a-zA-Z0-9]{4}-[a-zA-Z0-9]{12})?)$' );
		
	} else if( 'select' === $field['field_type'] && $field['custom_id'] == 'state' ){
		$field['field_options'] = '';
		$please_select = __('--- Please Select ---', 'solidcam');
		$country_states = [
			"United States" => [ $please_select,
				"Alabama", "Alaska", "Arizona", "Arkansas", "California", "Colorado", 
				"Connecticut", "Delaware", "Florida", "Georgia", "Hawaii", "Idaho", 
				"Illinois", "Indiana", "Iowa", "Kansas", "Kentucky", "Louisiana", 
				"Maine", "Maryland", "Massachusetts", "Michigan", "Minnesota", 
				"Mississippi", "Missouri", "Montana", "Nebraska", "Nevada", 
				"New Hampshire", "New Jersey", "New Mexico", "New York", 
				"North Carolina", "North Dakota", "Ohio", "Oklahoma", "Oregon", 
				"Pennsylvania", "Rhode Island", "South Carolina", "South Dakota", 
				"Tennessee", "Texas", "Utah", "Vermont", "Virginia", "Washington", 
				"West Virginia", "Wisconsin", "Wyoming"
			],
			"Canada" => [ $please_select,
				"Alberta", "British Columbia", "Manitoba", "New Brunswick", 
				"Newfoundland and Labrador", "Northwest Territories", 
				"Nova Scotia", "Nunavut", "Ontario", "Prince Edward Island", 
				"Quebec", "Saskatchewan", "Yukon"
			],
			"India" => [ $please_select,
				"Andhra Pradesh", "Arunachal Pradesh", "Assam", "Bihar", 
				"Chhattisgarh", "Delhi", "Goa", "Gujarat", "Haryana", 
				"Himachal Pradesh", "Jammu & Kashmir", "Jharkhand", "Karnataka", 
				"Kerala", "Maharashtra", "Madhya Pradesh", "Manipur", "Meghalaya", 
				"Mizoram", "Nagaland", "Odisha", "Punjab", "Rajasthan", "Sikkim", 
				"Tamil Nadu", "Tripura", "Telangana", "Uttar Pradesh", 
				"Uttarakhand", "West Bengal"
			]
		];
		
		if( is_user_logged_in() ){
			$user_id = get_current_user_id();
			$state = get_user_meta( $user_id, 'state', true );
			if( $state )
				$form_widget->add_render_attribute( 'select' . $field_index, 'data-state-selected', $state );
		}

		$form_widget->add_render_attribute( 'select' . $field_index, 'data-states', json_encode($country_states) );
		
		
	} else if( 'select' === $field['field_type'] && $field['custom_id'] == 'country' ){
			// Full list of countries sorted alphabetically
			$countries = [
				__( '--- Please Select ---', 'solidcam' ),
				__( 'Afghanistan', 'solidcam' ),
				__( 'Albania', 'solidcam' ),
				__( 'Algeria', 'solidcam' ),
				__( 'Andorra', 'solidcam' ),
				__( 'Angola', 'solidcam' ),
				__( 'Antigua and Barbuda', 'solidcam' ),
				__( 'Argentina', 'solidcam' ),
				__( 'Armenia', 'solidcam' ),
				__( 'Australia', 'solidcam' ),
				__( 'Austria', 'solidcam' ),
				__( 'Azerbaijan', 'solidcam' ),
				__( 'Bahamas', 'solidcam' ),
				__( 'Bahrain', 'solidcam' ),
				__( 'Bangladesh', 'solidcam' ),
				__( 'Barbados', 'solidcam' ),
				__( 'Belarus', 'solidcam' ),
				__( 'Belgium', 'solidcam' ),
				__( 'Belize', 'solidcam' ),
				__( 'Benin', 'solidcam' ),
				__( 'Bhutan', 'solidcam' ),
				__( 'Bolivia', 'solidcam' ),
				__( 'Bosnia and Herzegovina', 'solidcam' ),
				__( 'Botswana', 'solidcam' ),
				__( 'Brazil', 'solidcam' ),
				__( 'Brunei', 'solidcam' ),
				__( 'Bulgaria', 'solidcam' ),
				__( 'Burkina Faso', 'solidcam' ),
				__( 'Burundi', 'solidcam' ),
				__( 'Cabo Verde', 'solidcam' ),
				__( 'Cambodia', 'solidcam' ),
				__( 'Cameroon', 'solidcam' ),
				__( 'Canada', 'solidcam' ),
				__( 'Central African Republic', 'solidcam' ),
				__( 'Chad', 'solidcam' ),
				__( 'Chile', 'solidcam' ),
				__( 'China', 'solidcam' ),
				__( 'Colombia', 'solidcam' ),
				__( 'Comoros', 'solidcam' ),
				__( 'Congo (Congo-Brazzaville)', 'solidcam' ),
				__( 'Costa Rica', 'solidcam' ),
				__( 'Croatia', 'solidcam' ),
				__( 'Cuba', 'solidcam' ),
				__( 'Cyprus', 'solidcam' ),
				__( 'Czechia (Czech Republic)', 'solidcam' ),
				__( 'Denmark', 'solidcam' ),
				__( 'Djibouti', 'solidcam' ),
				__( 'Dominica', 'solidcam' ),
				__( 'Dominican Republic', 'solidcam' ),
				__( 'Ecuador', 'solidcam' ),
				__( 'Egypt', 'solidcam' ),
				__( 'El Salvador', 'solidcam' ),
				__( 'Equatorial Guinea', 'solidcam' ),
				__( 'Eritrea', 'solidcam' ),
				__( 'Estonia', 'solidcam' ),
				__( 'Eswatini (fmr. "Swaziland")', 'solidcam' ),
				__( 'Ethiopia', 'solidcam' ),
				__( 'Fiji', 'solidcam' ),
				__( 'Finland', 'solidcam' ),
				__( 'France', 'solidcam' ),
				__( 'Gabon', 'solidcam' ),
				__( 'Gambia', 'solidcam' ),
				__( 'Georgia', 'solidcam' ),
				__( 'Germany', 'solidcam' ),
				__( 'Ghana', 'solidcam' ),
				__( 'Greece', 'solidcam' ),
				__( 'Grenada', 'solidcam' ),
				__( 'Guatemala', 'solidcam' ),
				__( 'Guinea', 'solidcam' ),
				__( 'Guinea-Bissau', 'solidcam' ),
				__( 'Guyana', 'solidcam' ),
				__( 'Haiti', 'solidcam' ),
				__( 'Holy See', 'solidcam' ),
				__( 'Honduras', 'solidcam' ),
				__( 'Hungary', 'solidcam' ),
				__( 'Iceland', 'solidcam' ),
				__( 'India', 'solidcam' ),
				__( 'Indonesia', 'solidcam' ),
				__( 'Iran', 'solidcam' ),
				__( 'Iraq', 'solidcam' ),
				__( 'Ireland', 'solidcam' ),
				__( 'Israel', 'solidcam' ),
				__( 'Italy', 'solidcam' ),
				__( 'Jamaica', 'solidcam' ),
				__( 'Japan', 'solidcam' ),
				__( 'Jordan', 'solidcam' ),
				__( 'Kazakhstan', 'solidcam' ),
				__( 'Kenya', 'solidcam' ),
				__( 'Kiribati', 'solidcam' ),
				__( 'Kuwait', 'solidcam' ),
				__( 'Kyrgyzstan', 'solidcam' ),
				__( 'Laos', 'solidcam' ),
				__( 'Latvia', 'solidcam' ),
				__( 'Lebanon', 'solidcam' ),
				__( 'Lesotho', 'solidcam' ),
				__( 'Liberia', 'solidcam' ),
				__( 'Libya', 'solidcam' ),
				__( 'Liechtenstein', 'solidcam' ),
				__( 'Lithuania', 'solidcam' ),
				__( 'Luxembourg', 'solidcam' ),
				__( 'Madagascar', 'solidcam' ),
				__( 'Malawi', 'solidcam' ),
				__( 'Malaysia', 'solidcam' ),
				__( 'Maldives', 'solidcam' ),
				__( 'Mali', 'solidcam' ),
				__( 'Malta', 'solidcam' ),
				__( 'Marshall Islands', 'solidcam' ),
				__( 'Mauritania', 'solidcam' ),
				__( 'Mauritius', 'solidcam' ),
				__( 'Mexico', 'solidcam' ),
				__( 'Micronesia', 'solidcam' ),
				__( 'Moldova', 'solidcam' ),
				__( 'Monaco', 'solidcam' ),
				__( 'Mongolia', 'solidcam' ),
				__( 'Montenegro', 'solidcam' ),
				__( 'Morocco', 'solidcam' ),
				__( 'Mozambique', 'solidcam' ),
				__( 'Myanmar (formerly Burma)', 'solidcam' ),
				__( 'Namibia', 'solidcam' ),
				__( 'Nauru', 'solidcam' ),
				__( 'Nepal', 'solidcam' ),
				__( 'Netherlands', 'solidcam' ),
				__( 'New Zealand', 'solidcam' ),
				__( 'Nicaragua', 'solidcam' ),
				__( 'Niger', 'solidcam' ),
				__( 'Nigeria', 'solidcam' ),
				__( 'North Korea', 'solidcam' ),
				__( 'North Macedonia', 'solidcam' ),
				__( 'Norway', 'solidcam' ),
				__( 'Oman', 'solidcam' ),
				__( 'Pakistan', 'solidcam' ),
				__( 'Palau', 'solidcam' ),
				__( 'Palestine State', 'solidcam' ),
				__( 'Panama', 'solidcam' ),
				__( 'Papua New Guinea', 'solidcam' ),
				__( 'Paraguay', 'solidcam' ),
				__( 'Peru', 'solidcam' ),
				__( 'Philippines', 'solidcam' ),
				__( 'Poland', 'solidcam' ),
				__( 'Portugal', 'solidcam' ),
				__( 'Qatar', 'solidcam' ),
				__( 'Romania', 'solidcam' ),
				__( 'Russia', 'solidcam' ),
				__( 'Rwanda', 'solidcam' ),
				__( 'Saint Kitts and Nevis', 'solidcam' ),
				__( 'Saint Lucia', 'solidcam' ),
				__( 'Saint Vincent and the Grenadines', 'solidcam' ),
				__( 'Samoa', 'solidcam' ),
				__( 'San Marino', 'solidcam' ),
				__( 'Sao Tome and Principe', 'solidcam' ),
				__( 'Saudi Arabia', 'solidcam' ),
				__( 'Senegal', 'solidcam' ),
				__( 'Serbia', 'solidcam' ),
				__( 'Seychelles', 'solidcam' ),
				__( 'Sierra Leone', 'solidcam' ),
				__( 'Singapore', 'solidcam' ),
				__( 'Slovakia', 'solidcam' ),
				__( 'Slovenia', 'solidcam' ),
				__( 'Solomon Islands', 'solidcam' ),
				__( 'Somalia', 'solidcam' ),
				__( 'South Africa', 'solidcam' ),
				__( 'South Korea', 'solidcam' ),
				__( 'South Sudan', 'solidcam' ),
				__( 'Spain', 'solidcam' ),
				__( 'Sri Lanka', 'solidcam' ),
				__( 'Sudan', 'solidcam' ),
				__( 'Suriname', 'solidcam' ),
				__( 'Sweden', 'solidcam' ),
				__( 'Switzerland', 'solidcam' ),
				__( 'Syria', 'solidcam' ),
				__( 'Tajikistan', 'solidcam' ),
				__( 'Tanzania', 'solidcam' ),
				__( 'Thailand', 'solidcam' ),
				__( 'Timor-Leste', 'solidcam' ),
				__( 'Togo', 'solidcam' ),
				__( 'Tonga', 'solidcam' ),
				__( 'Trinidad and Tobago', 'solidcam' ),
				__( 'Tunisia', 'solidcam' ),
				__( 'Turkey', 'solidcam' ),
				__( 'Turkmenistan', 'solidcam' ),
				__( 'Tuvalu', 'solidcam' ),
				__( 'Uganda', 'solidcam' ),
				__( 'Ukraine', 'solidcam' ),
				__( 'United Arab Emirates', 'solidcam' ),
				__( 'United Kingdom', 'solidcam' ),
				__( 'United States', 'solidcam' ),
				__( 'Uruguay', 'solidcam' ),
				__( 'Uzbekistan', 'solidcam' ),
				__( 'Vanuatu', 'solidcam' ),
				__( 'Venezuela', 'solidcam' ),
				__( 'Vietnam', 'solidcam' ),
				__( 'Yemen', 'solidcam' ),
				__( 'Zambia', 'solidcam' ),
				__( 'Zimbabwe', 'solidcam' ),
			];
			
			$countries = apply_filters( 'solidcam_countries_list', $countries );
			$field['field_options'] = implode( "\n", $countries );
	}

	return $field;
}

add_action( 'elementor_pro/forms/validation', 'conditionally_require_country_fields', 10, 2 );

function conditionally_require_country_fields( $record, $ajax_handler ) {
	// Get form fields and submitted data
	$raw_fields = $record->get( 'fields' );
	$country = isset( $raw_fields['country']['value'] ) ? $raw_fields['country']['value'] : '';

	if ( isset( $raw_fields['country'] ) && empty( $raw_fields['country']['value'] ) ) {
		$ajax_handler->add_error( 'country', __( 'The country field is required.', 'elementor-pro' ) );
	}

	// Determine which fields should be required based on the selected country
	$is_state_required = in_array( $country, ['United States', 'India', 'Canada'] );
	$is_language_required = ($country === 'Switzerland');

	// Validate state field if required
	if ( $is_state_required && isset( $raw_fields['state'] ) && empty( $raw_fields['state']['value'] ) ) {
		$ajax_handler->add_error( 'state', __( 'The state field is required for this country.', 'elementor-pro' ) );
	}

	// Validate preferred language field if required
	if ( $is_language_required && isset( $raw_fields['preferred_language'] ) && empty( $raw_fields['preferred_language']['value'] ) ) {
		$ajax_handler->add_error( 'preferred_language', __( 'The preferred language field is required for Switzerland.', 'elementor-pro' ) );
	}
}
