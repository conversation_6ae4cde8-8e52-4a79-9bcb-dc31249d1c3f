<?php

if (!defined('ABSPATH')) exit; // Exit if accessed directly

class MySolidCam_Download_Widget extends \Elementor\Widget_Base {

	// Define the widget name
	public function get_name() {
		return 'mysolidcam_download_widget';
	}

	// Define the widget title
	public function get_title() {
		return __('Addon Download Widget', 'solidcam');
	}

	// Define the widget icon
	public function get_icon() {
		return 'eicon-file-download';
	}

	// Define the widget categories
	public function get_categories() {
		return ['general'];
	}

	// Register controls for the widget
	protected function _register_controls() {
		$this->start_controls_section(
			'content_section',
			[
				'label' => __('Module Settings', 'solidcam'),
				'tab' => \Elementor\Controls_Manager::TAB_CONTENT,
			]
		);

		// Add a multi-select control for modules
		$this->add_control(
			'module_key',
			[
				'label' => __('Select Module', 'solidcam'),
				'type' => \Elementor\Controls_Manager::SELECT2,
				'options' => [
					'Add-on' => __('SolidCAM Add-in', 'solidcam'),
					'CAD+CAM' => __('SolidCAM CAD/CAM Suite', 'solidcam'),
					'IVCAM' => __('InventorCAM Add-in', 'solidcam'),
					'SCMV' => __('SolidCAM Maker Version', 'solidcam'),
					'SCSE' => __('SolidCAM Add-In for Solid Edge', 'solidcam'),
					'ICMV' => __('InventorCAM Maker Version', 'solidcam'),
				],
				'default' => 'Add-on',
				'multiple' => true,
			]
		);
		$this->add_control('enable_force_display', [
			'label' => __('Show this forcefully', 'solidcam'),
			'type' => Elementor\Controls_Manager::SWITCHER,
			'default' => 'no',
		]);

		$this->end_controls_section();
	}

	// Render the widget output on the frontend
	protected function render() {
		$current_user = wp_get_current_user();

		// Ensure the user is logged in
		if (!$current_user->exists()) {
			echo '<p>' . esc_html__('Please log in to view expiration details.', 'solidcam') . '</p>';
			return;
		}

		// Get widget settings
		$settings = $this->get_settings_for_display();
		$module_key = !empty($settings['module_key']) ? $settings['module_key'] : null;
		$enable_force_display = !empty($settings['enable_force_display']) ? $settings['enable_force_display'] : 'no';

		$user_id = $current_user->ID;
		
		// in_array('reseller', $current_user->roles) || in_array('staff', $current_user->roles) || in_array('partner', $current_user->roles)
		$enabled_modules = [];
		if( $enable_force_display == 'yes' ){
			$matched_license_data = [];
			$enabled_modules = [ 'Add-on', 'CAD+CAM', 'IVCAM', 'SCMV', 'SCSE', 'ICMV' ];
		} else {
			// Fetch user metadata
			$active_license = get_user_meta($user_id, 'active_user_license', true);
			$customer_modules = get_user_meta($user_id, 'customer_modules', true);
			
			// Validate customer modules
			if (empty($customer_modules) || !is_array($customer_modules)) {
				echo '<p>' . esc_html__('No modules available for the current user.', 'solidcam') . '</p>';
				return;
			}
			
			// Find matching license data
			$matched_array = array_filter($customer_modules, function ($sub_array) use ($active_license) {
				return isset($sub_array['license_number']) && $sub_array['license_number'] == $active_license;
			});
			
			$matched_license_data = reset($matched_array);
			
			if ( empty($matched_license_data) ) {
				echo '<p>' . esc_html__('No modules available for the current user.', 'solidcam') . '</p>';
				return;
			}
		}

		// Fetch global software data from ACF
		$softwares = get_field('softwares', 'option');

		// Format the software data for JSON compatibility
		$formatted_data = [];
		if (!empty($softwares)) {
			foreach ($softwares as $software) {
				$formatted_data[$software['internal_name']] = [
					'name' => $software['name'],
					'internal_name' => $software['internal_name'],
					'major_versions' => array_map(function($major) {
						return [
							'version' => $major['version'],
							'suffix' => $major['suffix'],
							'patch_versions' => array_map(function($patch) {
								return [
									'version' => $patch['version'],
									'released_date' => $patch['released_date'],
									'release_notes' => $patch['release_notes'],
									'msi' => $patch['msi'],
									'downloads' => array_map(function($download) {
										return [
											'url' => $download['url'],
											'language' => $download['language'],
										];
									}, is_array($patch['downloads']) ? $patch['downloads'] : [])
								];
							}, is_array($major['patch_versions']) ? $major['patch_versions'] : [])
						];
					}, is_array($software['major_versions']) ? $software['major_versions'] : [])
				];
			}
		}
		
		if( isset($_GET['shah']) ){
			print_r($softwares);
			die;
		}

		// Define available modules
		$available_modules = [
			'Add-on' => __('SolidCAM Add-in', 'solidcam'),
			'CAD+CAM' => __('SolidCAM CAD/CAM Suite', 'solidcam'),
			'IVCAM' => __('InventorCAM Add-in', 'solidcam'),
			'SCMV' => __('SolidCAM Maker Version', 'solidcam'),
			'SCSE' => __('SolidCAM Add-In for Solid Edge', 'solidcam'),
			'ICMV' => __('InventorCAM Maker Version', 'solidcam')
		];
		
		if( !is_array( $module_key ) ){
			$module_key = [$module_key];
		}		
		// Filter available modules based on selected keys
		if ($module_key && !empty($module_key)) {
			$available_modules = array_filter($available_modules, function($key) use ($module_key) {
				return in_array($key, $module_key);
			}, ARRAY_FILTER_USE_KEY);
		}
		
		

		// Fetch user-specific module overrides
		$user_modules_override = get_user_meta($user_id, 'user_modules_override', true);
		$user_modules_data = !empty($user_modules_override[$active_license]) ? $user_modules_override[$active_license] : [];
		
		
		// Render available modules
		foreach ($available_modules as $module_key => $module_internal) {
			$module_enable = isset( $matched_license_data[$module_key] ) ? $matched_license_data[$module_key] : false;
			if ( ( $module_enable && $module_enable != 'false' ) || in_array($module_key, $user_modules_data) || in_array($module_key, $enabled_modules) ) {
				$module_data = $formatted_data[$module_internal] ?? null;

				// Skip rendering if no module data is found
				if (!$module_data) continue;

				// Get the latest version info for default display
				$latest_version = !empty($module_data['major_versions'][0]['patch_versions'][0])
					? $module_data['major_versions'][0]['patch_versions'][0]
					: null;

				// Render module information
				?>
				<div class="download-addon-widget-wrp" data-json-dto="<?php echo esc_attr(json_encode($module_data)); ?>" data-default-language="en">

					<div class="versioning-link-wrp">
						<a class="versioning-link" href="<?php echo esc_url($latest_version['downloads'][0]['url'] ?? '#'); ?>">
							<?php echo esc_html($module_data['name']); ?>
							<span class="versioning-selected-parent">
								<?php echo esc_html($module_data['major_versions'][0]['version'] ?? ''); ?>
							</span>
							<span class="versioning-selected-suffix">
								<?php echo esc_html($module_data['major_versions'][0]['suffix'] ?? ''); ?>
							</span>
						</a>
					</div>

					<div class="versioning-data-row">
						<div class="data-left">
							<a class="data-left-link versioning-selected-version mysolidcam-versioning__link" href="<?php echo esc_url($latest_version['downloads'][0]['url'] ?? '#'); ?>">
								<?php echo esc_html($latest_version['version'] ?? ''); ?>
							</a>
							<h5 class="sub-heading">
								<?php echo esc_html__('Language:', 'solidcam'); ?> <span class="selected-language"><?php echo esc_html__('English', 'solidcam'); ?></span>
							</h5>
						</div>
						<a class="data-link-right mysolidcam-versioning__link" href="<?php echo esc_url($latest_version['downloads'][0]['url'] ?? '#'); ?>">
							<span style="font-size: 1.8em; color: text-white;"><i class="fal fa-arrow-to-bottom" aria-hidden="true"></i></span>
						</a>
					</div>

					<div class="version-release-date-wrp">
						<?php if (!empty($latest_version['released_date'])): ?>
						<span class="release-date-wrp">
							<i class="fa-light fa-calendar-arrow-up me-1"></i>
							<?php echo esc_html($latest_version['released_date']); ?>
						</span>
						<?php endif; ?>

						<?php if (!empty($latest_version['release_notes'])): ?>
						<span class="release-notes">
							|<a href="<?php echo esc_url($latest_version['release_notes']['url']); ?>" 
								class="mysolidcam-versioning-selected-release-notes" 
								target="_blank"><i class="fal fa-file-pdf me-2"></i><?php echo esc_html__('Release notes:', 'solidcam'); ?></a>
						</span>
						<?php endif; ?>
					</div>

					<div class="bottom-btns-wrp">
						<!-- Language Button -->
						<div class="button-wrp">
							<button type="button" class="">
								<?php echo esc_html__('Change Language', 'solidcam'); ?> 
							</button>
							<ul class="versioning-languages dropdown-menu"></ul>
						</div>

						<!-- Version Button -->
						<div class="button-wrp">
							<button type="button" class="">
								<?php echo esc_html__('Change Version', 'solidcam'); ?> 
							</button>
							<ul class="versioning-versions dropdown-menu"></ul>
						</div>

						<!-- Patch Button -->
						<div class="button-wrp">
							<button type="button" class="">
								<?php echo esc_html__('Select SP', 'solidcam'); ?> 
							</button>
							<ul class="versioning-versions-patches dropdown-menu"></ul>
						</div>

						<!-- MSI Download -->
						<?php if (!empty($latest_version['msi'])): ?>
						<div class="button-wrp last-btn">
							<a href="<?php echo esc_url($latest_version['msi']); ?>" 
							   class="mysolidcam-versioning-msi">
								<?php echo esc_html__('MSI Download', 'solidcam'); ?> 
							</a>
						</div>
						<?php endif; ?>
					</div>
				</div>
				<?php
			}
		}
	}
}